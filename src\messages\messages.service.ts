import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Message } from "./schemas/message.schema";
import { Model, SortOrder } from "mongoose";
import { PageOptionsDto } from "../utils/classes/page-options.class";
// import { BlogFilterOptionsDTO } from "src/blogs/dto/filter-options.class";
import { PagedData } from "../utils/classes/paged-data.class";
import { NotificationsService } from "src/notifications/notifications.service";

@Injectable()
export class MessagesService {
  constructor(
    @InjectModel(Message.name) private messageModel: Model<Message>,
    private readonly notificationsService: NotificationsService

  ) {}
  async create(createMessageDto: any) {
    const message= await this.messageModel.create(createMessageDto);
    

  }

  async findAll(queryParams: PageOptionsDto, filterOptions) {
    const sort = { [queryParams.sortField]: queryParams.order as SortOrder };
    const filters = filterOptions.filters ? filterOptions.filters : {};
    const query = this.messageModel.find(filters).skip(queryParams.skip);
    if (queryParams.limit) {
      query.limit(queryParams.limit);
    }
    if (queryParams.sortField && queryParams.order) {
      query.sort(sort);
    } else {
      query.sort({ updatedAt: "desc" });
    }
    if (filterOptions.fields) {
      query.select(filterOptions.fields.split(",").join(" "));
    }
    const results = await query;
    const count: number = await this.messageModel.countDocuments(filters);
    return new PagedData(results, count);
  }

  findOne(id: number) {
    return `This action returns a #${id} message`;
  }

  update(id: string, updateMessageDto: any) {
    return this.messageModel.updateOne({ _id: id }, updateMessageDto);
  }

  remove(id: number) {
    return `This action removes a #${id} message`;
  }
}
