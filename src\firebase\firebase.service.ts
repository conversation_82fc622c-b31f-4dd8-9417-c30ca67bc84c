import { Injectable } from '@nestjs/common';
import admin from './firebase.config';

@Injectable()
export class FirebaseService {
  async sendPushNotification(
    tokens: string[],
    title: string,
    body: string,
    data?: any,
  ): Promise<void> {
    const message = {
      notification: {
        title,
        body,
      },
      data,
      tokens, // The FCM token of the target device
    };

    try {
      const response = await admin.messaging().sendEachForMulticast(message);
    } catch (error) { }
  }
}
