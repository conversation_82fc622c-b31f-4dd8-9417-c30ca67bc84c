import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Req,
  Query,
  HttpException,
  HttpStatus,
  NotFoundException,
} from "@nestjs/common";
import { ChatsService } from "./chats.service";
import { CreateChatDto } from "./dto/create-chat.dto";
import { ApiBearerAuth, ApiTags } from "@nestjs/swagger";
import { MessagesService } from "../messages/messages.service";
import { PageOptionsDto } from "../utils/classes/page-options.class";
import { ChatGateway } from "../chat/chat.gateway";

@ApiBearerAuth()
@ApiTags("Chat")
@Controller("chats")
export class ChatsController {
  constructor(
    private readonly chatsService: ChatsService,
    private readonly messagesService: MessagesService,
    private readonly chatGateway: ChatGateway
  ) {}

  @Post("/send-message")
  async create(@Body() createChatDto: CreateChatDto, @Req() req) {
    const senderId = req["user"]["id"];
    const filter = {
      users: { $all: [senderId, createChatDto.receiverId] },
    };
    let chat = await this.chatsService.findOne(filter);

    if (!chat) {
      let newChat = await this.chatsService.create({
        users: [senderId, createChatDto.receiverId],
      });

      const message = await this.messagesService.create({
        sender: senderId,
        chatId: newChat["_id"],
        content: createChatDto.message,
      });

      newChat = await this.chatsService.update(
        { _id: newChat["_id"] },
        { lastMessage: message["_id"] }
      );

      this.chatGateway.sendToClient(
        createChatDto.receiverId,
        "newChat",
        newChat
      );
      return newChat;
    } else {
      const message = await this.messagesService.create({
        sender: senderId,
        chatId: chat["_id"],
        content: createChatDto.message,
      });

      chat = await this.chatsService.update(
        { _id: chat["_id"] },
        { lastMessage: message["_id"] }
      );

      // Use the sendToClient method of the ChatGateway to emit the message to the receiver
      this.chatGateway.sendToClient(
        createChatDto.receiverId,
        "newMessage",
        message
      );
      return chat;
    }
  }

  @Get("get-chats")
  findAll(@Query() queryParams: PageOptionsDto, @Req() req) {
    const senderId = req["user"]["id"];
    const filters = { users: { $in: [senderId] } };

    return this.chatsService.findAll(queryParams, {
      filters: filters,
      fields: "",
    });
  }

  @Get(":id/messages")
  async findOne(
    @Param("id") id: string,
    @Query() queryParams: PageOptionsDto,
    @Req() req
  ) {
    const senderId = req["user"]["id"];
    const filter = {
      _id: id,
    };
    let chat = await this.chatsService.findOne(filter);
    console.log(chat);
    if (!chat) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: "Chat Does not exist",
        },
        HttpStatus.BAD_REQUEST
      );
    }

    const messages = await this.messagesService.findAll(queryParams, {
      filters: { chatId: chat["_id"] },
    });

    if (!messages) {
      throw new NotFoundException(`Chat with ID ${id} not found`);
    }
    return messages;
  }

  @Delete(":id")
  async remove(@Param("id") id: string) {
    const res = await this.chatsService.remove({ _id: id });
    if (!res) {
      throw new NotFoundException(`Chat with ID ${id} not found`);
    }
    return res;
  }
}
