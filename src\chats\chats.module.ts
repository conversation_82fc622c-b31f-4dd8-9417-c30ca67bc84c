import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { ChatsService } from "./chats.service";
import { Chats<PERSON>ontroller } from "./chats.controller";
import { MongooseModule } from "@nestjs/mongoose";
import { Chat, ChatSchema } from "./schemas/chat.schema";
import { MessagesModule } from "../messages/messages.module";
import { WebSocketModule } from "../chat/web-socket.module";
import { Coach, CoachSchema } from "src/coach/schemas/coach.schema";
import { Client, ClientSchema } from "src/client/schemas/client.schema";
import { UploadImageModule } from "src/upload-image/upload-image.module";
import { Message, MessageSchema } from "src/messages/schemas/message.schema";

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Chat.name, schema: ChatSchema },
      { name: Coach.name, schema: CoachSchema },
      { name: Client.name, schema: ClientSchema },
      { name: Message.name, schema: MessageSchema },
    ]),
    MessagesModule,
    WebSocketModule,
    UploadImageModule,
  ],
  controllers: [ChatsController],
  providers: [ChatsService],
})
export class ChatsModule {}
